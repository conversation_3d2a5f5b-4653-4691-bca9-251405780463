import React, { useState } from 'react';
import { X, Camera, Edit2, Save, User, Mail, Phone } from 'lucide-react';
import { useFirebaseAuth } from '../../hooks/useFirebaseAuth';
import { chatApi } from '../../api/chatApi';
import toast from 'react-hot-toast';
import LoadingSpinner from '../common/LoadingSpinner';

const UserProfile = ({ isOpen, onClose }) => {
  const { user, updateProfile } = useFirebaseAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    bio: user?.bio || ''
  });

  if (!isOpen) return null;

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      
      // Update profile via API
      await chatApi.updateProfile(formData);
      
      // Update local auth context
      await updateProfile(formData);
      
      toast.success('Profile updated successfully');
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: user?.name || '',
      email: user?.email || '',
      phone: user?.phone || '',
      bio: user?.bio || ''
    });
    setIsEditing(false);
  };

  const handleAvatarChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // TODO: Implement avatar upload
    toast.info('Avatar upload coming soon!');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-whatsapp-message-bg rounded-lg w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-whatsapp-dark">
          <h2 className="text-xl font-semibold text-white">Profile</h2>
          <div className="flex items-center space-x-2">
            {isEditing && (
              <>
                <button
                  onClick={handleCancel}
                  className="p-2 text-whatsapp-gray hover:text-white transition-colors"
                  title="Cancel"
                >
                  <X className="w-5 h-5" />
                </button>
                <button
                  onClick={handleSave}
                  disabled={isLoading}
                  className="p-2 text-whatsapp-primary hover:text-whatsapp-secondary transition-colors"
                  title="Save"
                >
                  {isLoading ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    <Save className="w-5 h-5" />
                  )}
                </button>
              </>
            )}
            {!isEditing && (
              <button
                onClick={() => setIsEditing(true)}
                className="p-2 text-whatsapp-gray hover:text-white transition-colors"
                title="Edit profile"
              >
                <Edit2 className="w-5 h-5" />
              </button>
            )}
            <button
              onClick={onClose}
              className="p-2 text-whatsapp-gray hover:text-white transition-colors"
              title="Close"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Profile Content */}
        <div className="p-6">
          {/* Avatar Section */}
          <div className="flex flex-col items-center mb-6">
            <div className="relative">
              <img
                src={user?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user?.name || 'User')}&background=00a884&color=fff&size=128`}
                alt={user?.name}
                className="w-24 h-24 rounded-full object-cover border-4 border-whatsapp-primary"
              />
              {isEditing && (
                <label className="absolute bottom-0 right-0 bg-whatsapp-primary hover:bg-whatsapp-secondary p-2 rounded-full cursor-pointer transition-colors">
                  <Camera className="w-4 h-4 text-white" />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarChange}
                    className="hidden"
                  />
                </label>
              )}
            </div>
          </div>

          {/* Profile Fields */}
          <div className="space-y-4">
            {/* Name */}
            <div>
              <label className="flex items-center text-whatsapp-gray text-sm mb-2">
                <User className="w-4 h-4 mr-2" />
                Name
              </label>
              {isEditing ? (
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full bg-whatsapp-dark text-white rounded-lg px-3 py-2 input-focus"
                  placeholder="Enter your name"
                />
              ) : (
                <p className="text-white bg-whatsapp-dark rounded-lg px-3 py-2">
                  {user?.name || 'Not set'}
                </p>
              )}
            </div>

            {/* Email */}
            <div>
              <label className="flex items-center text-whatsapp-gray text-sm mb-2">
                <Mail className="w-4 h-4 mr-2" />
                Email
              </label>
              <p className="text-white bg-whatsapp-dark rounded-lg px-3 py-2 opacity-60">
                {user?.email || 'Not set'}
              </p>
              <p className="text-xs text-whatsapp-gray mt-1">
                Email cannot be changed
              </p>
            </div>

            {/* Phone */}
            <div>
              <label className="flex items-center text-whatsapp-gray text-sm mb-2">
                <Phone className="w-4 h-4 mr-2" />
                Phone
              </label>
              {isEditing ? (
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full bg-whatsapp-dark text-white rounded-lg px-3 py-2 input-focus"
                  placeholder="Enter your phone number"
                />
              ) : (
                <p className="text-white bg-whatsapp-dark rounded-lg px-3 py-2">
                  {user?.phone || 'Not set'}
                </p>
              )}
            </div>

            {/* Bio */}
            <div>
              <label className="flex items-center text-whatsapp-gray text-sm mb-2">
                <Edit2 className="w-4 h-4 mr-2" />
                Bio
              </label>
              {isEditing ? (
                <textarea
                  name="bio"
                  value={formData.bio}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full bg-whatsapp-dark text-white rounded-lg px-3 py-2 input-focus resize-none"
                  placeholder="Tell us about yourself..."
                />
              ) : (
                <p className="text-white bg-whatsapp-dark rounded-lg px-3 py-2 min-h-[80px]">
                  {user?.bio || 'No bio set'}
                </p>
              )}
            </div>
          </div>

          {/* Account Info */}
          <div className="mt-6 pt-6 border-t border-whatsapp-dark">
            <h3 className="text-whatsapp-gray text-sm font-medium mb-3">Account Information</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-whatsapp-gray">Member since:</span>
                <span className="text-white">
                  {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-whatsapp-gray">User ID:</span>
                <span className="text-white font-mono text-xs">
                  {user?._id || 'Unknown'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
