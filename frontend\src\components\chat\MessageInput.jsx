import React, { useState, useRef, useEffect } from 'react';
import { Send, Paperclip, Smile } from 'lucide-react';
import { useChatSocket } from '../../hooks/useChatSocket';
import { useParams } from 'react-router-dom';

const MessageInput = ({ onSendMessage, replyTo = null, onCancelReply = null }) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const textareaRef = useRef(null);
  const typingTimeoutRef = useRef(null);
  const { chatId } = useParams();
  const { startTyping, stopTyping } = useChatSocket();

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
  }, [message]);

  // <PERSON>le typing indicator
  const handleTyping = () => {
    if (!isTyping && chatId) {
      setIsTyping(true);
      startTyping?.(chatId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      stopTyping?.(chatId);
    }, 1000);
  };

  // Stop typing indicator on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (isTyping && chatId) {
        stopTyping?.(chatId);
      }
    };
  }, [isTyping, chatId, sendTypingIndicator]);

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!message.trim()) return;

    // Stop typing indicator
    if (isTyping && chatId) {
      setIsTyping(false);
      stopTyping?.(chatId);
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    }

    // Send message
    onSendMessage(message, replyTo);
    setMessage('');
    
    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleChange = (e) => {
    setMessage(e.target.value);
    handleTyping();
  };

  return (
    <div className="bg-whatsapp-message-bg p-4 border-t border-whatsapp-dark">
      {/* Reply Preview */}
      {replyTo && (
        <div className="mb-3 p-3 bg-whatsapp-dark rounded-lg border-l-4 border-whatsapp-primary">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-whatsapp-primary text-sm font-medium">
                Replying to {replyTo.senderName}
              </p>
              <p className="text-whatsapp-gray text-sm truncate">
                {replyTo.text}
              </p>
            </div>
            {onCancelReply && (
              <button
                onClick={onCancelReply}
                className="ml-2 text-whatsapp-gray hover:text-white transition-colors"
              >
                ×
              </button>
            )}
          </div>
        </div>
      )}

      {/* Message Input */}
      <form onSubmit={handleSubmit} className="flex items-end space-x-3">
        {/* Attachment Button */}
        <button
          type="button"
          className="p-2 text-whatsapp-gray hover:text-white hover:bg-whatsapp-dark rounded-full transition-colors"
          title="Attach file"
        >
          <Paperclip className="w-5 h-5" />
        </button>

        {/* Text Input */}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder="Type a message..."
            className="w-full bg-whatsapp-dark text-white placeholder-whatsapp-gray rounded-lg px-4 py-3 pr-12 resize-none input-focus"
            rows={1}
            style={{ minHeight: '48px', maxHeight: '120px' }}
          />
          
          {/* Emoji Button */}
          <button
            type="button"
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-whatsapp-gray hover:text-white transition-colors"
            title="Add emoji"
          >
            <Smile className="w-5 h-5" />
          </button>
        </div>

        {/* Send Button */}
        <button
          type="submit"
          disabled={!message.trim()}
          className={`p-3 rounded-full transition-all duration-200 ${
            message.trim()
              ? 'bg-whatsapp-primary hover:bg-whatsapp-secondary text-white'
              : 'bg-whatsapp-dark text-whatsapp-gray cursor-not-allowed'
          }`}
          title="Send message"
        >
          <Send className="w-5 h-5" />
        </button>
      </form>
    </div>
  );
};

export default MessageInput;
